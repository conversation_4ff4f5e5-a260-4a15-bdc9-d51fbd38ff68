"use client"

import { motion } from "framer-motion"
import { use<PERSON><PERSON><PERSON>, useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import Pagination from "@/components/ui/pagination"
import { useToast } from "@/components/ui/toast"

import { getAuthToken } from "@/utils/auth"
import AddUserModal from "./add-user-modal"
import EditUserModal from "./edit-user-modal"

// API响应类型定义
type ApiResponse<T> = {
  code: number
  data: T
  msg: string
}

// Users API响应数据结构
type UsersApiData = {
  total: number
  users: User[]
}

// 用户数据类型定义 - 匹配API响应结构
interface User {
  uid: number
  membership_id: number
  email_id: number
  username: string
  avatar_url: string
  name: string
  gender: string
  country: string
  organization: string
  phone: string
  position: string
  bio: string
  interest: string
  expertise: string
  paid: boolean
  u_create_time: string
  role: string
  mid: number
  membership: string
  Price: number
  eid: number
  email: string
  email_verified: boolean
  VerifiedTime: string
}

export default function AdminUsersPage() {
  const { addToast } = useToast()

  // 状态管理
  const [allUsers, setAllUsers] = useState<User[]>([]) // 所有缓存的数据
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMoreData, setHasMoreData] = useState(true)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // 分页配置
  const itemsPerPage = 20
  const fetchLimit = 100

  // 安全的日期格式化函数
  const formatDate = (dateString: string, options: { includeTime?: boolean } = {}) => {
    if (!isClient || !dateString) return ""
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return "Invalid Date"
      return options.includeTime ? date.toLocaleString() : date.toLocaleDateString()
    } catch (_error) {
      return "Invalid Date"
    }
  }

  // 获取用户数据的API函数
  const fetchUsers = useCallback(
    async (offset: number = 0, filter: string = "") => {
      setLoading(true)
      try {
        // 直接使用fetch调用API，因为新的数据结构
        const authToken = getAuthToken()
        if (!authToken) {
          addToast({
            type: "error",
            title: "Authentication Error",
            message: "Authentication required",
          })
          return
        }

        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
        const params = new URLSearchParams({
          limit: fetchLimit.toString(),
          offset: offset.toString(),
          filter: filter,
        })

        const response = await fetch(`${apiBaseUrl}/api/admin/users?${params}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const apiResponse = (await response.json()) as ApiResponse<UsersApiData>

        if (apiResponse.code !== 200) {
          throw new Error(apiResponse.msg || "Failed to fetch users")
        }

        const newUsers = (apiResponse.data.users || []).filter((user) => user && user.uid)

        if (offset === 0) {
          // 首次加载或重新搜索
          setAllUsers(newUsers)
        } else {
          // 加载更多数据
          setAllUsers((prev) => [...prev, ...newUsers])
        }

        setHasMoreData(newUsers.length === fetchLimit)
      } catch (error) {
        console.error("Error fetching users:", error)
        addToast({
          type: "error",
          title: "Error",
          message: error instanceof Error ? error.message : "Failed to fetch user data",
        })
      } finally {
        setLoading(false)
      }
    },
    [addToast, fetchLimit]
  )

  // 初始化数据加载
  useEffect(() => {
    setIsClient(true)
    fetchUsers(0, "")
  }, [fetchUsers])

  // 过滤和分页逻辑
  const filteredUsers = allUsers.filter((user) => {
    if (!user) return false
    const searchLower = searchTerm.toLowerCase()
    return (
      (user.name || "").toLowerCase().includes(searchLower) ||
      (user.email || "").toLowerCase().includes(searchLower) ||
      (user.username || "").toLowerCase().includes(searchLower) ||
      (user.organization || "").toLowerCase().includes(searchLower)
    )
  })

  // 分页逻辑
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex)

  // 处理搜索输入变化（不立即搜索）
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  // 处理回车搜索
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setCurrentPage(1)
      fetchUsers(0, searchTerm)
    }
  }

  // 加载更多数据（当需要时）
  const loadMoreData = useCallback(() => {
    if (hasMoreData && !loading) {
      fetchUsers(allUsers.length, searchTerm)
    }
  }, [hasMoreData, loading, allUsers.length, searchTerm, fetchUsers])

  const handleUserAdded = (_newUser: unknown) => {
    // 刷新用户列表而不是直接添加，因为类型不匹配
    fetchUsers(0, searchTerm)
  }

  const handleUserUpdated = (_updatedUser: User) => {
    // 刷新整个用户列表以获取最新数据
    fetchUsers(0, searchTerm)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setShowEditModal(true)
  }

  const getMembershipLabel = (membershipName: string) => {
    return membershipName || "Unknown"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage registered users and their permissions</p>
        </div>
        <AddUserModal onUserAdded={handleUserAdded} />
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search users by name, email, or username... (Press Enter to search)"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onKeyPress={handleSearchKeyPress}
                className="w-full"
              />
            </div>
            <Button variant="outline">
              <i className="fas fa-filter mr-2"></i>
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* User List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>All Users ({filteredUsers.length})</CardTitle>
            {hasMoreData && (
              <Button variant="outline" onClick={loadMoreData} disabled={loading}>
                {loading ? "Loading..." : "Load More"}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Username</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Name</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Email</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Role</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Membership</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Country</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Organization</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Email Verified</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Paid</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Registered</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedUsers.map((user) => {
                  if (!user || !user.uid) return null
                  return (
                    <motion.tr
                      key={user.uid}
                      className="border-b border-gray-100 hover:bg-gray-50"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <td className="px-4 py-3 font-medium">{user.username || "-"}</td>
                      <td className="px-4 py-3">{user.name || "-"}</td>
                      <td className="px-4 py-3">{user.email || "-"}</td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="outline"
                          className={
                            user.role === "admin"
                              ? "border-purple-200 bg-purple-50 text-purple-700"
                              : "border-blue-200 bg-blue-50 text-blue-700"
                          }
                        >
                          {user.role || "user"}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">
                        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
                          {getMembershipLabel(user.membership)}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">{user.country || "-"}</td>
                      <td className="px-4 py-3">
                        <div className="max-w-32 truncate text-sm text-gray-600" title={user.organization || ""}>
                          {user.organization || "-"}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="outline"
                          className={
                            user["email_verified"]
                              ? "border-green-200 bg-green-50 text-green-700"
                              : "border-red-200 bg-red-50 text-red-700"
                          }
                        >
                          {user["email_verified"] ? "Verified" : "Unverified"}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="outline"
                          className={
                            user.paid
                              ? "border-green-200 bg-green-50 text-green-700"
                              : "border-yellow-200 bg-yellow-50 text-yellow-700"
                          }
                        >
                          {user.paid ? "Paid" : "Unpaid"}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-sm">{formatDate(user.u_create_time)}</td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleEditUser(user)}
                            title="Edit user"
                          >
                            <i className="fas fa-edit text-gray-500"></i>
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* 分页组件 */}
          {totalPages > 1 && (
            <div className="mt-6 border-t pt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                totalItems={filteredUsers.length}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Modal */}
      <EditUserModal
        user={editingUser}
        open={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setEditingUser(null)
        }}
        onUserUpdated={handleUserUpdated}
      />
    </div>
  )
}
